using InventoryManagement.DataAccess;
using InventoryManagement.Models;
using InventoryManagement.Services;
using InventoryManagement.Views;
using InventoryManagement.ViewModels;
using InventoryManagement.Controls;
using InventoryManagement.Infrastructure.LazyLoading;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Windows;
using System.Windows.Threading;
using System.Linq;
using System.Threading.Tasks;
using InventoryManagement.Infrastructure.OfflineMode;
using InventoryManagement.Infrastructure.Configuration;
using InventoryManagement.Infrastructure.ErrorHandling;
using InventoryManagement.Services;
using IErrorHandlingService = InventoryManagement.Infrastructure.ErrorHandling.IErrorHandlingService;
using InventoryManagement.Infrastructure.Startup;

namespace InventoryManagement
{
    /// <summary>
    /// Main application class, focused purely on offline desktop functionality
    /// with three user roles: Ad<PERSON>, Basement Manager, and Cashier
    /// </summary>
    public partial class App : Application
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<App> _logger;
        private readonly IErrorHandlingService _errorHandler;
        
        // Static property to hold the current user
        public static User CurrentUser { get; set; }
        
        // Static property to track offline status
        public static bool IsOfflineMode { get; private set; }
        
        // Static reference to the current app instance for easier service access
        public static App Current => (App)Application.Current;
        
        // Static property for service locator to simplify service access
        public static ServiceLocator ServiceLocator { get; private set; }

        // Connection status indicator that can be accessed from anywhere
        public static ConnectionStatusIndicator ConnectionStatus { get; private set; }

        // Service provider for dependency injection access
        public IServiceProvider ServiceProvider => _serviceProvider;

        public App()
        {
            // Configure services
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // Get core services
            _logger = _serviceProvider.GetRequiredService<ILogger<App>>();
            _errorHandler = _serviceProvider.GetRequiredService<IErrorHandlingService>();

            // Set up global exception handlers
            SetupExceptionHandling();

            _logger.LogInformation("Tom General Trading Inventory Management System starting...");
        }

        private void ConfigureServices(IServiceCollection services)
        {
            try
            {
                // Add configuration
                services.AddAppConfiguration();

                // Add all offline-optimized services
                services.AddOfflineCoreServices(_serviceProvider.GetRequiredService<IConfiguration>());

                // Register views
                services.AddTransient<MainWindow>();
                services.AddTransient<LoginWindow>();
                services.AddTransient<DashboardWindow>();
                services.AddTransient<ComprehensiveMainDashboard>();
                services.AddTransient<SetupWizardWindow>();
                services.AddTransient<SettingsWindow>();
                services.AddTransient<BackupRestoreWindow>();

                // Register ViewModels
                services.AddTransient<ComprehensiveMainDashboardViewModel>();

                // Register user-friendly services
                services.AddSingleton<IUserFriendlyMessageService, UserFriendlyMessageService>();

                // Register startup services
                services.AddTransient<ApplicationStartup>();

                // Validate configuration
                _serviceProvider.ValidateConfiguration();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error configuring services: {ex.Message}\n\nThe application will now exit.",
                    "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Current?.Shutdown(-1);
            }
        }

        private void SetupExceptionHandling()
        {
            AppDomain.CurrentDomain.UnhandledException += (s, e) =>
                _errorHandler.HandleException((Exception)e.ExceptionObject, "AppDomain.UnhandledException", true);

            DispatcherUnhandledException += (s, e) =>
            {
                _errorHandler.HandleException(e.Exception, "App.DispatcherUnhandledException", true);
                e.Handled = true;
            };

            TaskScheduler.UnobservedTaskException += (s, e) =>
            {
                _errorHandler.HandleException(e.Exception, "TaskScheduler.UnobservedTaskException", true);
                e.SetObserved();
            };
        }

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                base.OnStartup(e);

                // Initialize application with system PostgreSQL
                var startup = _serviceProvider.GetRequiredService<ApplicationStartup>();
                await startup.InitializeAsync();

                // Update offline mode status from configuration
                var config = _serviceProvider.GetRequiredService<AppConfiguration>();
                IsOfflineMode = config.Offline.EnableOfflineMode;

                // Show login window
                var loginWindow = _serviceProvider.GetRequiredService<LoginWindow>();
                loginWindow.Show();
            }
            catch (Exception ex)
            {
                _errorHandler.HandleException(ex, "App.OnStartup", true, ErrorSeverity.Critical);
                Shutdown(-1);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _logger.LogInformation("Application shutting down...");

            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }

            base.OnExit(e);
        }
    }
}
